<template>
  <a-drawer :open="visible" :width="1200" title="商品上新" @close="handleClose" :maskClosable="false" :keyboard="false" :destroyOnClose="true">
    <a-spin :spinning="loading">
      <a-steps :current="current" :items="stepList" class="mb-20px px-20px"></a-steps>
      <ProductAddStepFirst v-if="current === 0" v-model:form="baseForm" ref="basicFormRef" />
      <ProductAddStepSecond v-if="current === 1" v-model:form="baseForm" ref="attrFormRef" v-model:form-config="transformTemplateConfig" :rules="secondRules" />
      <ProductAddStepThird v-show="current === 2" v-model:form="baseForm" v-model:loading="loading" />
    </a-spin>
    <template #footer>
      <a-space>
        <a-button v-if="current > 0" @click="handlePrev">上一步</a-button>
        <a-button v-if="current < 2" type="primary" @click="handleNext">下一步</a-button>
        <a-button v-else-if="current === 2" type="primary" @click="handleSubmit">提交</a-button>
        <a-button @click="handleSaveDraft">保存草稿</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { CreateProduct } from '@/servers/ProductLibrary'
import { cloneDeep } from '@/utils'
import { Rule } from 'ant-design-vue/es/form'
import { type BaseFormItem } from '@/hook/useBaseForm'
import { message } from 'ant-design-vue'
import ProductAddStepSecond from './ProductAddStepSecond.vue'
import ProductAddStepFirst from './ProductAddStepFirst.vue'
import ProductAddStepThird from './ProductAddStepThird.vue'
import ProductAddStepSecondTable from './ProductAddStepSecondTable.vue'

const basicFormRef = ref<InstanceType<typeof ProductAddStepFirst>>()
const attrFormRef = ref<InstanceType<typeof ProductAddStepSecond>>()

const emit = defineEmits(['new'])

const { proxy }: any = getCurrentInstance()

const secondRules = ref<Record<string, Rule[]>>({})

// 抽屉是否显示
const visible = defineModel<boolean>('visible', { required: true })
// 当前步骤
const current = ref(0)
// 加载状态
const loading = ref(false)
// 基础信息表单验证规则
const baseForm = ref<any>({
  product_type: 1,
  main_images_ids: [],
  sku_images_ids: [],
  product_detail_images_ids: [],
  product_detail_en_images_ids: [],
  productCertificates: [],
})
// 步骤列表
const stepList = ref([
  {
    title: '基础信息',
  },
  {
    title: '商品属性',
  },
  {
    title: '商品图文设置',
  },
])

const handlePrev = () => {
  current.value--
}

const handleNext = async () => {
  if (current.value === 0) {
    await basicFormRef.value?.validate?.()
    loading.value = true
    await getCategoryTemplate()
    current.value++
    loading.value = false
  } else {
    await attrFormRef.value?.validate?.()
    current.value++
  }
}

// 模板配置
const templateConfig = ref<any[]>([])
// 转换后的模板配置
const transformTemplateConfig = ref<BaseFormItem[]>([])

// 获取商品类目模板
const getCategoryTemplate = async () => {
  const id = baseForm.value.category_id[baseForm.value.category_id.length - 1].split('_')[1]
  const [newTransformTemplateConfig, newTemplateConfig, newSecondRules] = await getTemplate(Number(id), baseForm)
  templateConfig.value = newTemplateConfig
  transformTemplateConfig.value = newTransformTemplateConfig
  secondRules.value = newSecondRules
}

// 提交
const handleSubmit = async () => {
  const form = cloneDeep(baseForm.value)
  const ls = form.category_id[form.category_id.length - 1].split('_')
  form.category_id = Number(ls[0])
  form.category_template_id = Number(ls[1])
  form.publish_status = 1

  const productAttrValues = transformSubmitForm(form)
  form.productAttrValues = productAttrValues
  delete form.agreed_selling_price
  try {
    await CreateProduct(form)
    proxy.$confirm.show({
      title: '提示',
      content: h('div', null, [h('div', { class: 'text-center' }, '成功提交商品资料。'), h('div', { class: 'text-center' }, '您可以到商品库查看已提交商品。')]),
      confirmText: '商品库',
      cancelText: '继续上传',
      onConfirm: () => {
        visible.value = false
      },
      onCancel: () => {
        emit('new')

        nextTick(() => {
          visible.value = false
        })
      },
    })
  } catch (error: any) {
    proxy.$confirm.show({
      title: '提示',
      content: h('div', null, [h('div', null, '上传商品失败，请修改后重新上传：'), h('div', null, `① ${error?.message || ''}`)]),
      confirmText: '返回修改',
      cancelText: '关闭',
      onConfirm: () => {},
      onCancel: () => {
        visible.value = false
      },
    })
  }
}

// 保存
const handleSaveDraft = async () => {
  const form = cloneDeep(baseForm.value)
  if (form.category_id) {
    const ls = form.category_id[form.category_id.length - 1].split('_')
    form.category_id = Number(ls[0])
    form.category_template_id = Number(ls[1])
  } else {
    form.category_id = null
    form.category_template_id = null
  }
  await basicFormRef?.value?.clearValidate()
  await basicFormRef.value?.validate(['product_name', 'supplier_product_number'])
  const productAttrValues = transformSubmitForm(form)
  form.productAttrValues = productAttrValues
  form.publish_status = 0
  await CreateProduct(form)
  message.success('保存草稿成功')
  visible.value = false
}

// 转换提交表单
const transformSubmitForm = (form: any) => {
  const arr: any[] = []

  templateConfig.value.forEach((i) => {
    if (i.children) {
      i.children.forEach((j: any) => {
        if (form[j.id]) {
          const item: any = {
            id: 0,
            attr_id: j.id,
            attr_group_id: j.attr_group_id,
            attr_name: j.name,
          }
          if (i.type === 100) {
            item.value = form[j.id]?.map((i: any) => i.id)?.join(',')
          } else if (j.type === 7 && j.display_type === 2) {
            item.value = form[j.id].toString()
          } else if (j.type === 12 && j.type_json.option_type === 2) {
            item.value = form[j.id].toString()
          } else {
            item.value = form[j.id]
          }
          arr.push(item)
          delete form[j.id]
        }
      })
    }
  })
  return arr
}

// 关闭抽屉
const handleClose = () => {
  proxy.$confirm.show({
    title: '取消',
    width: 450,
    content: '当前商品上新信息未保存，是否确认取消配置？',
    onConfirm: () => {
      visible.value = false
    },
  })
}

// 自定义插槽回调
const slotCallback = (item: any): BaseFormItem => ({
  type: 'slot',
  label: '',
  key: item.name,
  slots: () => h(ProductAddStepSecondTable, { item, form: baseForm.value }),
})

const { getTemplate } = useTemplate(slotCallback, attrFormRef)
</script>

<style lang="scss" scoped></style>
