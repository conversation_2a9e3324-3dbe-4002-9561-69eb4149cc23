<template>
  <div class="main">
    <div v-if="showAllStatus?.length">
      <a-tabs class="filterTabs w-full" v-model:active-key="queryParams.selection_status" @change="handleAllStatusChange">
        <a-tab-pane v-for="item in showAllStatus" :key="item.value">
          <template #tab>
            <a-badge :count="statusCount[item.key]" :offset="[12, -2]" :overflowCount="100000">
              <span>{{ item.label }}</span>
            </a-badge>
          </template>
        </a-tab-pane>
      </a-tabs>
    </div>
    <SearchForm v-model:form="formArr" :page-type="PageType.PRODUCT_LIBRARY" @search="search" @setting="tableRef?.showTableSetting()" />
    <BaseTable ref="tableRef" :page-type="PageType.PRODUCT_LIBRARY" v-model:form="formArr" :get-list="getListFn" :is-index="true">
      <template #left-btn>
        <a-button type="primary" @click="handleNewProduct" :disabled="!btnPermission[820001]">商品上新</a-button>
      </template>
      <template #right-btn>
        <a-select v-model:value="exportSelectValue" :disabled="!btnPer.export" class="w-120px">
          <a-select-option v-for="item in exportSelect" :key="item.value" :value="item.value" :label="item.label">
            {{ item.label }}
          </a-select-option>
        </a-select>
        <a-button @click="handleExport" :disabled="!btnPer.export">导出</a-button>
      </template>
      】

      <template #main_image="{ row }">
        <div class="aa">
          <a-image
            v-if="row.images_view_url"
            :src="row.images_view_url"
            :class="lineHeightType == 1 ? '!w-40 !h-30' : lineHeightType == 2 ? '!w-50 !h-40' : '!w-60 !h-50'"
            :preview="{
              onVisibleChange: (previewVisible) => setPreviewVisible(row, previewVisible),
              src: row.bigImagesUrl,
            }"
          >
            <template #previewMask>
              <EyeOutlined />
            </template>
          </a-image>
        </div>
      </template>
      <template #supplier_product_status_string="{ row }">
        <a-tag :color="row.supplier_product_status === 1 ? 'green' : 'red'">{{ row.supplier_product_status === 1 ? '启用' : '停用' }}</a-tag>
      </template>
      <template #publish_status_string="{ row }">
        <span class="decoration-underline cursor-pointer" @click="hadnleShowReleaseList(row.id)">{{ row.publish_status_string }}</span>
      </template>
      <template #declared_purchase_tax_price="{ row }">
        <span v-if="![null, undefined, ['']].includes(row.declared_purchase_tax_price)" class="c-#2b78ef">￥{{ row.declared_purchase_tax_price.toFixed(2) }}</span>
        <span v-else>--</span>
      </template>
      <template #agreed_purchase_tax_price="{ row }">
        <span v-if="![null, undefined, ['']].includes(row.agreed_purchase_tax_price) && row.selection_status === 40" class="c-#e0524f">￥{{ row.agreed_purchase_tax_price.toFixed(2) }}</span>
        <span v-else>--</span>
      </template>
      <template #selection_notes="{ row }">
        <span v-if="row.selection_notes && [50, 60].includes(row.selection_status)">{{ row.selection_notes }}</span>
        <span v-else>--</span>
      </template>
      <template #selection_status_string="{ row }">
        <span>{{ row.selection_status_string === '选品审核中' ? '待选品' : row.selection_status_string }}</span>
      </template>
      <template #operate="{ row }">
        <a-button type="text" @click="handleViewProduct(row.id, row.publish_status === 0)" v-if="row.publish_status !== 0" :disabled="!btnPer.view">查看</a-button>
        <a-button type="text" @click="handleEditProduct(row.id, row.publish_status === 0)" :disabled="!btnPer.edit || row.selection_status === 60">编辑</a-button>
        <a-button type="text" @click="handleChangeStatus(row.id, 'enable')" v-if="row.supplier_product_status === 0 && row.publish_status !== 0" :disabled="!btnPer.enable">启用</a-button>
        <a-button type="text" @click="handleChangeStatus(row.id, 'disable')" v-if="row.supplier_product_status === 1 && row.publish_status !== 0" :disabled="!btnPer.disable">停用</a-button>
      </template>
    </BaseTable>
    <Enable ref="enableRef" @search="search" />
    <component v-if="productComponent.visible" v-model:visible="productComponent.visible" :is="productComponent.component" v-bind="productComponent.props" @new="handleNew" @query="search"></component>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { EyeOutlined } from '@ant-design/icons-vue'
import { PageType } from '@/common/enum'
import { GetList, GetCategoryOption, GetProductPreviewUrl, GetLabelStatusCount } from '@/servers/ProductLibrary'
import { getCommonOption } from '@/utils'
import { usePermission } from '@/hook/usePermission'
import { Add as AddDownloadTask } from '@/servers/DownloadCenter'
import SearchForm from '@/components/SearchForm/index.vue'
import BaseTable from '@/components/BaseTable/index.vue'
import useBadgeStore from '@/store/modules/badgeStore'
import ProductAddDrawer from './components/ProductAddDrawer.vue'
import ProductViewDrawer from './components/ProductViewDrawer.vue'
import ProductEditDrawer from './components/ProductEditDrawer.vue'
import ProductReleaseListModal from './components/ProductReleaseListModal.vue'
import Enable from './components/Enable.vue'

const badgeStore = useBadgeStore()
const { btnPermission } = usePermission()
// 导出字段
const exportSelectValue = ref(1)
// 导出下拉框
const exportSelect = ref([
  {
    label: '导出全部',
    value: 1,
  },
  {
    label: '导出所选',
    value: 2,
  },
  {
    label: '导出筛选条件',
    value: 3,
  },
])
// 搜索功能
const search = () => {
  tableRef.value.search()
}

const queryParams = ref<any>({
  selection_status: null,
})

const allStatus = ref([
  {
    label: '全部',
    value: null,
    key: '',
    permission: '822000',
  },
  {
    label: '待选商品',
    value: 0,
    key: 'waiting_count',
    permission: '823000',
  },
  {
    label: '完成选品',
    value: 40,
    key: '',
    permission: '824000',
  },
  {
    label: '已驳回',
    value: 50,
    key: '',
    permission: '825000',
  },
  {
    label: '已拒绝',
    value: 60,
    key: '',
    permission: '826000',
  },
])

const showAllStatus = computed(() => allStatus.value.filter((item) => btnPermission.value[item.permission]))

const btnPer = computed<any>(() => {
  switch (queryParams.value.selection_status) {
    case null:
      return {
        view: btnPermission.value[822001],
        edit: btnPermission.value[822002],
        disable: btnPermission.value[822003],
        export: btnPermission.value[822004],
      }
    case 0:
      return {
        view: btnPermission.value[823001],
        edit: btnPermission.value[823002],
        disable: btnPermission.value[823003],
        export: btnPermission.value[823004],
      }
    case 40:
      return {
        view: btnPermission.value[824001],
        edit: btnPermission.value[824002],
        disable: btnPermission.value[824003],
        export: btnPermission.value[824004],
      }
    case 50:
      return {
        view: btnPermission.value[825001],
        edit: btnPermission.value[825002],
        disable: btnPermission.value[825003],
        export: btnPermission.value[825004],
      }
    case 60:
      return {
        view: btnPermission.value[826001],
        edit: btnPermission.value[826002],
        disable: btnPermission.value[826003],
        export: btnPermission.value[826004],
      }
    default:
      return {}
  }
})

const statusCount = ref({})

const lineHeightType = computed(() => tableRef.value.lineHeightType)

const getListFn = async (params: any) => {
  if (!showAllStatus.value?.length) return
  params.is_get_total = true
  params.selection_status = queryParams.value.selection_status ?? params.new_selection_status
  if (Array.isArray(params.category_id_list) && params.category_id_list.length) {
    params.category_id_list = params.category_id_list.map((i) => i[i.length - 1])
  }
  getLabelStatusCount(params)
  const res = await GetList(params)
  return res
}

const productComponent = ref<any>({
  visible: false,
  component: null,
  props: {},
})

// 启用禁用ref
const enableRef = ref<any>()
// 表格和表单引用
const tableRef = ref()
// 搜索表单配置
const formArr: any = ref([
  {
    label: '供应商商品编码',
    value: null,
    type: 'batch-input',
    width: 240,
    key: 'supplier_product_number',
  },
  {
    label: '平台商品编码',
    value: null,
    type: 'batch-input',
    width: 240,
    key: 'product_number',
  },
  {
    label: '商品名称(中)',
    value: null,
    type: 'input',
    key: 'product_name',
  },
  {
    label: '商品名称(英)',
    value: null,
    type: 'input',
    key: 'product_name_en',
  },
  {
    label: '商品品牌',
    value: [],
    type: 'select',
    key: 'brand_id_list',
    mode: 'multiple',
    options: [],
  },
  {
    label: '商品类目',
    value: [],
    type: 'cascader',
    key: 'category_id_list',
    multiple: true,
    options: [],
    fieldNames: { label: 'label', value: 'value', children: 'children' },
  },
  {
    label: '申报采购单价',
    value: [null, null],
    type: 'range-input',
    placeholder: ['申报采购单价', '申报采购单价'],
    formKeys: ['min_declared_purchase_tax_price', 'max_declared_purchase_tax_price'],
  },
  {
    label: '议定采购单价',
    value: [null, null],
    type: 'range-input',
    placeholder: ['议定采购单价', '议定采购单价'],
    formKeys: ['min_agreed_purchase_tax_price', 'max_agreed_purchase_tax_price'],
  },
  {
    label: '选品状态',
    value: null,
    key: 'new_selection_status',
    type: 'select',
    options: [
      {
        label: '待选商品',
        value: 0,
      },
      {
        label: '完成选品',
        value: 40,
      },
      {
        label: '已驳回',
        value: 50,
      },
      {
        label: '已拒绝',
        value: 60,
      },
    ],
  },
  // {
  //   label: '选品时间',
  //   value: [],
  //   type: 'range-picker',
  //   key: 'selection_time',
  //   formKeys: ['selection_time_start', 'selection_time_end'],
  //   placeholder: ['选品时间', '选品时间'],
  //   valueFormat: 'YYYY-MM-DD',
  // },
  {
    label: '状态',
    value: null,
    type: 'select',
    key: 'supplier_product_status',
    options: [],
  },
  {
    label: '发布状态',
    value: null,
    type: 'select',
    key: 'publish_status',
    options: [],
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'create_at',
    formKeys: ['create_at_start', 'create_at_end'],
    placeholder: ['创建开始日期', '创建结束日期'],
    valueFormat: 'YYYY-MM-DD',
  },
  {
    label: '最后修改时间',
    value: null,
    type: 'range-picker',
    options: [],
    key: 'modified_at',
    formKeys: ['modified_at_start', 'modified_at_end'],
    placeholder: ['修改开始日期', '修改结束日期'],
    valueFormat: 'YYYY-MM-DD',
  },
  {
    label: '操作人',
    value: null,
    type: 'input',
    key: 'operated_by',
  },
])

// 查看
const handleViewProduct = (id: number, isEdit: boolean) => {
  productComponent.value.visible = true
  productComponent.value.component = ProductViewDrawer
  productComponent.value.props = {
    id,
    isEdit,
  }
}
// 编辑
const handleEditProduct = (id: number, isNew) => {
  productComponent.value.visible = true
  productComponent.value.component = ProductEditDrawer
  productComponent.value.props = {
    id,
    isEdit: true,
    isNew,
  }
}
// 发布状态列表
const hadnleShowReleaseList = (id: number) => {
  productComponent.value.visible = true
  productComponent.value.component = ProductReleaseListModal
  productComponent.value.props = {
    id,
    onQuery: search(),
  }
}

const handleNew = () => {
  setTimeout(() => {
    handleNewProduct()
  }, 500)
}

// 商品上新
const handleNewProduct = () => {
  productComponent.value.visible = true
  productComponent.value.component = ProductAddDrawer
  productComponent.value.props = {}
}
// 点击预览时才调接口
const setPreviewVisible = async (row: any, visible: boolean) => {
  if (visible) {
    try {
      const imgRes = await GetProductPreviewUrl({
        fileId: row.main_images_id,
        width: 999,
        height: 999,
      })
      row.bigImagesUrl = imgRes.data.view_url
    } catch (e) {
      row.bigImagesUrl = row.images_view_url
    }
  }
}

// 启用禁用
const handleChangeStatus = (id: number, type: string) => {
  enableRef.value.showModal(id, type)
}
// 导出
const handleExport = async () => {
  const ids = tableRef.value.checkItemsArr.map((item: any) => item.id)
  const createAt = formArr.value.find((item: any) => item.key === 'create_at')?.value
  const modifiedAt = formArr.value.find((item: any) => item.key === 'modified_at')?.value

  let exportPageType = 1 // 默认为全部页面
  const currentStatus = queryParams.value.selection_status

  if (currentStatus === null) {
    exportPageType = 11 // 全部页面
  } else if (currentStatus === 0) {
    exportPageType = 12 // 待选品
  } else if (currentStatus === 50) {
    exportPageType = 15 // 已驳回
  } else if (currentStatus === 40) {
    exportPageType = 14 // 完成选品
  } else if (currentStatus === 60) {
    exportPageType = 16 // 已拒绝
  }
  const params: any = {
    export_type: exportSelectValue.value,
    ids,
    filter: {
      supplier_product_number: formArr.value.find((item: any) => item.key === 'supplier_product_number')?.value, // 供应商商品编码
      product_number: formArr.value.find((item: any) => item.key === 'product_number')?.value, // 平台商品编码
      product_name: formArr.value.find((item: any) => item.key === 'product_name')?.value, // 商品名称
      product_name_en: formArr.value.find((item: any) => item.key === 'product_name_en')?.value, // 商品名称(英)
      brand_id_list: formArr.value.find((item: any) => item.key === 'brand_id_list')?.value, // 商品品牌
      supplier_product_status: formArr.value.find((item: any) => item.key === 'supplier_product_status')?.value, // 状态
      publish_status: formArr.value.find((item: any) => item.key === 'publish_status')?.value, // 发布状态
      create_at_start: Array.isArray(createAt) && createAt[0] ? new Date(createAt[0]).toISOString() : undefined, // 创建开始日期
      create_at_end: Array.isArray(createAt) && createAt[1] ? new Date(createAt[1]).toISOString() : undefined, // 创建结束日期
      modified_at_start: Array.isArray(modifiedAt) && modifiedAt[0] ? new Date(modifiedAt[0]).toISOString() : undefined, // 修改开始日期
      modified_at_end: Array.isArray(modifiedAt) && modifiedAt[1] ? new Date(modifiedAt[1]).toISOString() : undefined, // 修改结束日期
      operated_by: formArr.value.find((item: any) => item.key === 'operated_by')?.value, // 操作人
      min_declared_purchase_tax_price: formArr.value.find((i) => i.label === '申报采购单价').value![0] || undefined,
      max_declared_purchase_tax_price: formArr.value.find((i) => i.label === '申报采购单价').value![1] || undefined,
      min_agreed_purchase_tax_price: formArr.value.find((i) => i.label === '议定采购单价').value![0] || undefined,
      max_agreed_purchase_tax_price: formArr.value.find((i) => i.label === '议定采购单价').value![1] || undefined,
      selection_status: queryParams.value.selection_status ?? formArr.value.find((item: any) => item.key === 'new_selection_status')?.value,
    },
    page_export_type: exportPageType,
  }

  const category_id_list = formArr.value.find((item: any) => item.key === 'category_id_list')?.value
  if (Array.isArray(category_id_list) && category_id_list.length) {
    params.filter.category_id_list = category_id_list.map((i) => i[i.length - 1])
  }
  if (exportSelectValue.value == 1) {
    if (queryParams.value.selection_status === null) {
      delete params.filter
    } else {
      params.filter = {
        selection_status: queryParams.value.selection_status,
      }
    }
    delete params.ids
  }
  if (exportSelectValue.value == 2) {
    delete params.filter
    if (ids.length == 0) {
      message.warning('请勾选要导出的商品')
      return
    }
  }
  if (exportSelectValue.value == 3) {
    delete params.ids
  }

  try {
    // 使用新的下载中心接口
    const downloadParams = {
      file_name: '商品导出',
      export_type_identifier: '6',
      export_params: params as any,
    }

    const res = await AddDownloadTask(downloadParams)
    if (res.success) {
      message.success('导出任务已添加到下载队列，请到下载中心查看进度')
    } else {
      message.error(res.message || '添加导出任务失败')
    }
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请重试')
  }
}
const getCertificateType = async () => {
  const [certificateOptions, productBrandOptions, releaseOptions] = await getCommonOption([11, 19, 21])
  formArr.value.forEach((item) => {
    if (item.key === 'supplier_product_status') {
      item.options = certificateOptions
    }
    if (item.key === 'brand_id_list') {
      item.options = productBrandOptions
    }
    if (item.key === 'publish_status') {
      item.options = releaseOptions
    }
  })
}
const getCategoryOption = async () => {
  const res = await GetCategoryOption()
  const formatTreeData = (categoryOptions) => {
    return categoryOptions.map((item) => ({
      label: item.name,
      value: item.id,
      children: item.children?.length ? formatTreeData(item.children) : undefined,
    }))
  }
  const treeData = formatTreeData(res.data)
  formArr.value.forEach((item) => {
    if (item.key === 'category_id_list') {
      item.options = treeData
    }
  })
}

const getLabelStatusCount = async (params: any) => {
  const isNeedParamsRequest = false
  if (isNeedParamsRequest) {
    const res = await GetLabelStatusCount(params)
    statusCount.value = res.data
  } else {
    getRedNumber()
  }
}

const handleAllStatusChange = () => {
  const selectionStatusItem = formArr.value.find((i) => i.key === 'new_selection_status')
  selectionStatusItem.type = queryParams.value.selection_status === null ? 'select' : ''
  selectionStatusItem.value = null
  search()
}

const getRedNumber = async () => {
  badgeStore.fetchBadgeCounts().then(() => {
    statusCount.value = badgeStore.badgeCounts?.productLabelStatusCount
  })
}

watch(
  () => productComponent.value.visible,
  (newVal) => {
    if (!newVal) {
      tableRef.value?.maintainSearch()
    }
  },
)

onMounted(() => {
  getCertificateType()
  getCategoryOption()
  setTimeout(() => {
    if (showAllStatus.value.length) {
      queryParams.value.selection_status = showAllStatus.value[0].value
      search()
    }
  }, 50)
})
</script>

<style lang="scss" scoped>
:deep(.ant-radio-group) {
  .ant-badge {
    &:not(:first-child) {
      .ant-radio-button-wrapper {
        margin-left: -1px;
        border-end-start-radius: 0;
      }
    }

    &:last-child {
      .ant-radio-button-wrapper {
        border-end-end-radius: 4px;
      }
    }
  }

  .ant-badge-count {
    z-index: 2;
    min-width: 30px !important;
  }
}
</style>
