<template>
  <div class="main">
    <a-tabs class="filterTabs w-full" v-model:active-key="queryParams.status" @change="handleStatusChange">
      <a-tab-pane v-for="item in statusTabs" :key="item.value">
        <template #tab>
          <a-badge :count="item.redNum" :offset="[12, -2]" :overflowCount="100000">
            <span>{{ item.label }}</span>
          </a-badge>
        </template>
      </a-tab-pane>
    </a-tabs>
    <SearchForm v-model:form="formArr" :page-type="PageType.SHIPORDER_MANAGE" @search="search" @setting="tableRef?.showTableSetting()" />
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.SHIPORDER_MANAGE" :get-list="getList" :merge-cells="mergeCells">
      <template #total_purchase_scheduled_quantity_header>
        <div class="text-center m-b--6">已发货数</div>
        <div class="text-center">(已预约入库数)</div>
      </template>
      <template #scheduled_quantity_header>
        <div class="text-center m-b--6">本次发货数</div>
        <div class="text-center">(本次预约入库数)</div>
      </template>
      <template #shop="{ row }">
        <div class="flex items-center vxe-render-image">
          <BaseImage :src="row.image_url" />
          <div class="ml-8 c-#333" :style="{ lineHeight: autoLineHeight + 'px' }">
            <div>{{ row.sku_name }}</div>
            <div class="lh-20px">
              <span class="c-#999 mr-4">类目:</span>
              <span>{{ row.all_category || '--' }}</span>
            </div>
          </div>
        </div>
      </template>
      <template #shop_code="{ row }">
        <div :style="{ lineHeight: autoLineHeight + 'px' }">
          <div>
            <span class="c-#999 mr-4">供应商:</span>
            <span>{{ row.srs_supplier_prod_code || '--' }}</span>
          </div>
          <div>
            <span class="c-#999 mr-4">平台:</span>
            <span>{{ row.srs_platform_prod_code || '--' }}</span>
          </div>
        </div>
      </template>
      <template #audit_status_str="{ row }">
        <BaseBadge v-if="row.audit_status_str" :label="row.audit_status_str" :type="shipOrderStatusMap[row.audit_status]" />
        <span v-else>--</span>
      </template>
      <template #creator="{ row }">
        <div :style="{ lineHeight: autoLineHeight + 'px' }">
          <div>
            <span class="c-#999 mr-4">创建人:</span>
            <span>{{ row.creator_name || '--' }}</span>
          </div>
          <div>
            <span class="c-#999 mr-4">创建时间:</span>
            <span>{{ row.create_at || '--' }}</span>
          </div>
        </div>
      </template>
      <template #operate="{ row }">
        <a-space>
          <a-button v-if="btnPermission[920001]" type="text" @click="handleView(row, 'detail')">查看</a-button>
          <a-button v-if="btnPermission[920002] && ![95, 100].includes(row.audit_status)" type="text" @click="handleView(row, 'editLogist')">修改物流</a-button>
        </a-space>
      </template>
    </BaseTable>
    <ViewDrawer ref="viewRef" />
  </div>
</template>

<script setup lang="ts">
import { VxeTablePropTypes } from 'vxe-table'
import SearchForm from '@/components/SearchForm/index.vue'
import { PageType } from '@/common/enum'
import BaseTable from '@/components/BaseTable/index.vue'
import { getShipOrderList } from '@/servers/ShipmentManage'
import { usePermission } from '@/hook/usePermission'
import useBadgeStore from '@/store/modules/badgeStore'
import ViewDrawer from './components/ViewDrawer.vue'

const { btnPermission } = usePermission()
const badgeStore = useBadgeStore()
const tableRef = useTemplateRef<InstanceType<typeof BaseTable>>('tableRef')
const autoLineHeight = computed(() => (tableRef?.value?.lineHeightType === 1 ? 16 : 24))

// 查看ref
const viewRef = ref()
const mergeCells = ref<VxeTablePropTypes.MergeCells>([])

// [ 待提审=10, 待审核=20, 已通过=90, 已拒绝=95, 已完成=100, 已作废=200 ]
// 发货单状态颜色
const shipOrderStatusMap = {
  10: 'warning',
  20: 'warning',
  90: 'info',
  95: 'error',
  100: 'success',
  200: 'default',
}

const formArr = ref([
  {
    label: '发货预约单号',
    value: null,
    type: 'batch-input',
    width: 240,
    key: 'number',
  },
  {
    label: '采购订单编号',
    value: null,
    type: 'batch-input',
    width: 240,
    key: 'purcharse_order_number',
  },
  {
    label: '供应商商品编码',
    value: null,
    type: 'batch-input',
    width: 240,
    key: 'srs_supplier_prod_code',
  },
  {
    label: '平台商品编码',
    value: null,
    type: 'batch-input',
    width: 240,
    key: 'srs_platform_prod_code',
  },
  {
    label: '商品名称',
    value: null,
    type: 'input',
    options: [],
    key: 'product_name',
    placeholder: '商品名称',
  },
  {
    label: '收货仓库',
    value: null,
    type: 'input',
    options: [],
    key: 'warehouse_name',
    placeholder: '请输入收货仓库',
  },
  {
    label: '创建人',
    value: null,
    type: 'input',
    options: [],
    key: 'creator_name',
    placeholder: '请输入创建人',
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['create_at_start', 'create_at_end'],
    placeholder: ['创建开始时间', '创建结束时间'],
  },
  {
    label: '预计到货',
    value: null,
    type: 'range-picker',
    key: 'scheduled_arrival_time',
    formKeys: ['scheduled_arrival_time_start', 'scheduled_arrival_time_end'],
    placeholder: ['预计到货开始时间', '预计到货结束时间'],
  },
])

const queryParams = ref({
  status: 0,
})

const statusTabs = ref([
  {
    label: '全部',
    value: 0,
    redNum: 0,
  },
  {
    label: '待提审',
    value: 10,
    redNum: 0,
  },
  {
    label: '待审核',
    value: 20,
    redNum: 0,
  },
  {
    label: '已通过',
    value: 90,
    redNum: 0,
  },
  {
    label: '已完成',
    value: 100,
    redNum: 0,
  },
  {
    label: '已拒绝',
    value: 95,
    redNum: 0,
  },
])

const search = () => tableRef.value?.search()

const getList = async (obj) => {
  obj.number && (obj.number = obj.number.split(','))
  obj.purcharse_order_number && (obj.purcharse_order_number = obj.purcharse_order_number.split(','))
  obj.srs_supplier_prod_code && (obj.srs_supplier_prod_code = obj.srs_supplier_prod_code.split(','))
  obj.srs_platform_prod_code && (obj.srs_platform_prod_code = obj.srs_platform_prod_code.split(','))
  obj.audit_status = statusTabs.value.find((item) => item.value === queryParams.value.status)?.value
  obj.is_get_booking_order_details = true
  const res = await getShipOrderList(obj)
  const columns = tableRef.value?.tableRef?.getColumns() || []
  const mergeList: any[] = []
  mergeCells.value = []
  let findIndex = 0
  const list = res.data.list.flatMap((item: any, index: number) => {
    item.purchase_order_numbers = item.purchase_order_numbers.toString().replaceAll(',', '；')
    if (item.booking_order_detail_list_results?.length > 1) {
      // 为每个需要合并的列创建合并规则
      columns.forEach((col, colIndex) => {
        const mergeKeys = [
          'image_url',
          'sku_name',
          'all_category',
          'srs_supplier_prod_code',
          'srs_platform_prod_code',
          'total_purchase_quantity',
          'total_purchase_scheduled_quantity',
          'scheduled_quantity',
          'total_actual_inbound',
        ]
        if (!mergeKeys.includes(col.field)) {
          mergeList.push({
            row: index + findIndex,
            col: colIndex,
            rowspan: item.booking_order_detail_list_results.length,
            colspan: 1,
          })
        }
      })
      findIndex += item.booking_order_detail_list_results.length - 1
    }
    if (!item.booking_order_detail_list_results?.length) {
      return {
        ...item,
        idx: index + 1 + (tableRef.value!.page - 1) * tableRef.value!.pageSize,
        pId: item.id,
      }
    }
    console.log('item.', item)
    return item.booking_order_detail_list_results.map((product: any, idx: number) => {
      product.pId = item.id
      return {
        ...item,
        ...product,
        id: `${item.id}_${idx}`,
        idx: index + 1 + (tableRef.value!.page - 1) * tableRef.value!.pageSize,
      }
    })
  })
  nextTick(() => {
    mergeCells.value = mergeList
    console.log('mergeCells.value', mergeCells.value)
    // 设置行高
  })
  return {
    data: {
      list,
      total: res.data.total,
    },
  }
}

// 查看
const handleView = (row: any, type: string) => {
  const useData = localStorage.getItem('userData')
  const userData = useData ? JSON.parse(useData) : {}
  const ids = tableRef.value?.tableData.map((i) => i.pId)
  const uniqueIds = [...new Set(ids)] as number[]
  viewRef.value.showDrawer(row.pId, userData.id, uniqueIds, type)
}

const handleStatusChange = () => {
  search()
}

const getRedNumber = async () => {
  badgeStore.fetchBadgeCounts().then(() => {
    const statusCount = badgeStore.badgeCounts?.bookingOrderLabelStatusCount
    statusTabs.value[1].redNum = statusCount?.pending_submit_count || 0
    statusTabs.value[3].redNum = statusCount?.approved_count || 0
  })
}

getRedNumber()
</script>

<style scoped></style>
