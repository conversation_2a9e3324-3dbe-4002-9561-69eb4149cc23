<template>
  <div>
    <div class="drawer-title">商品图片/视频</div>
    <a-form layout="vertical">
      <a-form-item label="商品主图" :rules="{ required: true }">
        <div class="flex">
          <div>
            <BaseFileUpload
              class="uploader main-image"
              multiple
              :max-count="6"
              v-model:file-list="fileObj.shopFileList"
              :before-upload="(file) => beforeUpload(file, 'shopFileList')"
              accept="image/png, image/jpeg, image/gif"
              @delete="(f) => handleDelete(f, 'shopFileList')"
            >
              <div v-if="fileObj.shopFileList.length < 6">
                <plus-outlined />
              </div>
            </BaseFileUpload>
          </div>
          <div class="c-#999">
            <div>1. ≥800×800像素，单图≤3MB，支持 JPEG/JPG/PNG/GIF 格式。</div>
            <div>2. 支持同时上传多张图片，最多不超过6张。</div>
            <div>3. 图片包含商品整体款式和商品细节。第一张图片将作为商品首张主图。</div>
          </div>
        </div>
      </a-form-item>
      <a-form-item label="商品SKU图" :rules="{ required: true }">
        <div class="flex">
          <div>
            <BaseFileUpload
              class="uploader"
              multiple
              :max-count="6"
              v-model:file-list="fileObj.skuFileList"
              accept="image/png, image/jpeg, image/gif"
              :before-upload="(file) => beforeUpload(file, 'skuFileList')"
              @delete="(f) => handleDelete(f, 'skuFileList')"
            >
              <div v-if="fileObj.skuFileList.length < 6">
                <plus-outlined />
              </div>
            </BaseFileUpload>
          </div>
          <div class="c-#999">
            <div>1. 用于首页、列表页、活动 等特定场景的展示</div>
            <div>2. 比例 1:1，≥800×800像素，单图&lt; =3MB，支持 JPEG/JPG/PNG/GIF 格式，&lt;=6张。</div>
            <div>3. 背景纯白底，主体清晰，展示完整，不能有LOGO、文字。</div>
          </div>
        </div>
      </a-form-item>
      <a-form-item label="商品视频">
        <div class="flex">
          <div>
            <BaseFileUpload
              class="uploader"
              v-model:file-list="fileObj.videoFileList"
              list-type="picture-card"
              :before-upload="(file) => beforeUpload(file, 'videoFileList')"
              @delete="(f) => handleDelete(f, 'videoFileList')"
              accept="video/mp4"
              :max-count="1"
            >
              <div v-if="fileObj.videoFileList.length < 1">
                <SvgIcon name="video" class="w-20px! h-20px!" />
              </div>
            </BaseFileUpload>
          </div>
          <div class="c-#999">
            <div>1. 商品支持 MP4 格式，≤300MB。仅支持上传1个视频。</div>
            <div>2. 视频时长 15秒~60秒，分辨率 ≥720（推荐1080P）， 宽高比 1:1（正方形）或 16:9（横屏）。</div>
            <div>3. 建议上传视频，可帮助用户更好了解商品。</div>
          </div>
        </div>
      </a-form-item>
      <div class="drawer-title">商品详情</div>
      <a-radio-group v-model:value="language" button-style="solid" class="mb-8">
        <a-radio-button value="cn">中文</a-radio-button>
        <a-radio-button value="en">英文</a-radio-button>
      </a-radio-group>
      <a-form-item :rules="{ required: true }">
        <template #label>
          <a-space :size="16">
            <span>商品详情页</span>
            <div class="flex items-center c-#999">
              <InfoCircleOutlined />
              支持 JPEG/JPG/PNG/GIF 格式；单张≤5MB；宽度>=750px，建议 750px~1600px（高度自适应）；最多不超过15张。
            </div>
          </a-space>
        </template>
        <div class="c-#D33333">我们将按照提交时的图片顺序展示详情页图片，请严格按照详情页顺序上传或调整，以确保详情页正确展示。</div>
        <BaseFileUploadMode
          class="mt-16"
          :max-count="15"
          v-model:file-list="fileObj.cnFileList"
          v-show="language === 'cn'"
          :before-upload="(file) => beforeUpload(file, 'cnFileList')"
          @delete="(f) => handleDelete(f, 'cnFileList')"
          @sortable="(f) => handleSortable(f, 'cnFileList')"
        />
        <BaseFileUploadMode
          class="mt-16"
          v-model:file-list="fileObj.enFileList"
          v-show="language === 'en'"
          :max-count="15"
          :before-upload="(file) => beforeUpload(file, 'enFileList')"
          @delete="(f) => handleDelete(f, 'enFileList')"
          @sortable="(f) => handleSortable(f, 'enFileList')"
        />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { PlusOutlined, InfoCircleOutlined } from '@ant-design/icons-vue'
import { UploadCommonFile, UploadVideoFile } from '@/servers/Common'
import BaseFileUpload from '@/components/BaseFileUpload/index.vue'
import BaseFileUploadMode from '@/components/BaseFileUploadMode/index.vue'
import SvgIcon from '@/components/SvgIcon/index.vue'
import { message } from 'ant-design-vue'
import { createId } from '@/utils'

const form = defineModel<any>('form', { required: true })

// 商品主图
const fileObj = ref<any>({
  shopFileList: [],
  skuFileList: [],
  videoFileList: [],
  cnFileList: [],
  enFileList: [],
})

const language = ref('cn')

const beforeUpload = async (file: any, type: string) => {
  file.status = 'uploading'
  file.fileId = createId()

  if (['shopFileList', 'skuFileList'].includes(type)) {
    if (file.size > 3 * 1024 * 1024) {
      message.error('图片大小不能超过3M')
      return false
    }
    if (type === 'shopFileList') {
      if (fileObj.value.shopFileList.length >= 6) {
        message.error('最多上传6张图片')
        return false
      }
    } else if (type === 'skuFileList') {
      if (fileObj.value.skuFileList.length >= 6) {
        message.error('最多上传6张图片')
        return false
      }
    }
    if (!['png', 'jpg', 'jpeg', 'gif', 'PNG', 'JPG', 'JPEG', 'GIF'].includes(file.name.split('.').pop()!)) {
      message.error('请上传png、jpg、jpeg、gif格式图片')
      return false
    }
  }

  if (type === 'videoFileList') {
    if (file.size > 300 * 1024 * 1024) {
      message.error('视频大小不能超过300M')
      return false
    }
    if (file.type.indexOf('mp4') === -1) {
      message.error('请上传mp4格式视频')
      return false
    }
  }
  if (type === 'cnFileList' || type === 'enFileList') {
    if (file.size > 5 * 1024 * 1024) {
      message.error('图片大小不能超过5M')
      return false
    }
    if (type === 'cnFileList') {
      if (fileObj.value.cnFileList.length >= 15) {
        message.error('最多上传15张图片')
        return false
      }
      if (!['png', 'jpg', 'jpeg', 'gif', 'PNG', 'JPG', 'JPEG', 'GIF'].includes(file.name.split('.').pop()!)) {
        message.error('请上传png、jpg、jpeg、gif格式图片')
        return false
      }
    } else if (type === 'enFileList') {
      if (fileObj.value.enFileList.length >= 15) {
        message.error('最多上传15张图片')
        return false
      }
      if (!['png', 'jpg', 'jpeg', 'gif', 'PNG', 'JPG', 'JPEG', 'GIF'].includes(file.name.split('.').pop()!)) {
        message.error('请上传png、jpg、jpeg、gif格式图片')
        return false
      }
    }
  }

  fileObj.value[type].push(file)
  const formData = new FormData()
  formData.append('files', file)
  try {
    const fn = type === 'videoFileList' ? UploadVideoFile : UploadCommonFile
    const res = await fn('Product', formData)
    file.status = 'done'
    file.url = URL.createObjectURL(file)
    file.id = res.data[0].id
    file.fileId = res.data[0].id
    fileObj.value[type] = fileObj.value[type].filter((i: any) => i.fileId !== file.fileId)
    fileObj.value[type].push(file)
    switch (type) {
      case 'shopFileList':
        form.value.main_images_ids = fileObj.value.shopFileList.map((i: any) => i.id).slice(0, 6)
        break
      case 'skuFileList':
        form.value.sku_images_ids = fileObj.value.skuFileList.map((i: any) => i.id).slice(0, 6)
        break
      case 'videoFileList':
        file.url = await getVideoCoverByStream(file)
        file.videoUrl = URL.createObjectURL(file)
        form.value.video_file_id = res.data[0]!.id
        break
      case 'cnFileList':
        form.value.product_detail_images_ids = fileObj.value.cnFileList.map((i: any) => i.id).slice(0, 15)

        break
      case 'enFileList':
        form.value.product_detail_en_images_ids = fileObj.value.enFileList.map((i: any) => i.id).slice(0, 15)
        break
      default:
        break
    }
  } catch (error) {
    fileObj.value[type] = fileObj.value[type].filter((i: any) => i.fileId !== file.fileId)
  }
  return false
}

const handleSortable = (file: any, type: string) => {
  if (type === 'cnFileList') {
    form.value.product_detail_images_ids = file.map((i: any) => i.id)
  } else if (type === 'enFileList') {
    form.value.product_detail_en_images_ids = file.map((i: any) => i.id)
  }
}

const getVideoCoverByStream = (file: Blob): Promise<string> => {
  return new Promise((resolve) => {
    const url = URL.createObjectURL(file)
    const video = document.createElement('video')
    video.src = url
    video.crossOrigin = 'anonymous'
    video.currentTime = 0.1 // 避免黑帧
    video.addEventListener(
      'canplay',
      () => {
        const canvas = document.createElement('canvas')
        canvas.width = video.videoWidth
        canvas.height = video.videoHeight
        const ctx = canvas.getContext('2d')
        ctx?.drawImage(video, 0, 0, canvas.width, canvas.height)
        resolve(canvas.toDataURL('image/png'))
        URL.revokeObjectURL(url)
      },
      { once: true },
    )
  })
}

const handleDelete = (file: any, type: string) => {
  fileObj.value[type].splice(fileObj.value[type].indexOf(file), 1)
  switch (type) {
    case 'shopFileList':
      form.value.main_images_ids = form.value.main_images_ids.filter((i: any) => i !== file.id)
      fileObj.value.shopFileList = fileObj.value.shopFileList.filter((i: any) => i.fileId !== file.fileId)
      break
    case 'skuFileList':
      form.value.sku_images_ids = form.value.sku_images_ids.filter((i: any) => i !== file.id)
      fileObj.value.skuFileList = fileObj.value.skuFileList.filter((i: any) => i.fileId !== file.fileId)
      break
    case 'videoFileList':
      form.value.video_file_id = null
      fileObj.value.videoFileList = fileObj.value.videoFileList.filter((i: any) => i.fileId !== file.fileId)
      break
    case 'cnFileList':
      form.value.product_detail_images_ids = form.value.product_detail_images_ids.filter((i: any) => i !== file.id)
      fileObj.value.cnFileList = fileObj.value.cnFileList.filter((i: any) => i.fileId !== file.fileId)
      break
    case 'enFileList':
      form.value.product_detail_en_images_ids = form.value.product_detail_en_images_ids.filter((i: any) => i !== file.id)
      fileObj.value.enFileList = fileObj.value.enFileList.filter((i: any) => i.fileId !== file.fileId)
      break
    default:
      break
  }
}
</script>

<style scoped lang="scss">
:deep(.base-file-upload) {
  .ant-upload.ant-upload-select {
    width: 58px !important;
    height: 58px !important;
  }

  .ant-upload-list-item-container {
    position: relative;
    width: 58px !important;
    height: 58px !important;
    overflow: hidden !important;
  }

  .ant-upload-list-item {
    padding: 0 !important;

    &::before {
      width: 58px !important;
      height: 58px !important;
    }
  }
}

:deep(.main-image) {
  .ant-upload-list {
    .ant-upload-list-item-container {
      &:first-child {
        position: relative;

        &::before {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
          width: 100%;
          height: 20px;
          font-size: 10px;
          line-height: 18px;
          color: #fff;
          text-align: center;
          content: '首张主图';
          background-color: #73a2f3;
        }
      }
    }
  }
}
</style>
